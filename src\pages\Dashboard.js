import React, { useState, useEffect } from 'react';
import {
  Md<PERSON>ttachMoney,
  MdReceipt,
  MdStorefront,
  MdPeople,
  MdLocalShipping,
  MdPersonAdd,
  MdTrendingUp,
  MdTrendingDown
} from 'react-icons/md';
import { firestore } from '../firebaseConfig';
import { collection, query, where, getDocs, Timestamp } from 'firebase/firestore';
import './Dashboard.css';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    sales: {
      value: '₹ 0',
      change: '0%',
      trend: 'up'
    },
    billCut: {
      value: '0',
      change: '0%',
      trend: 'up'
    },
    activeOutlets: {
      value: '0',
      change: '0%',
      trend: 'up'
    },
    activeDS: {
      value: '0',
      change: '0%',
      trend: 'up'
    },
    activeWD: {
      value: '0',
      change: '0%',
      trend: 'up'
    },
    newOnboarding: {
      value: '0',
      change: '0%',
      trend: 'up'
    }
  });

  // Define fetchDashboardData outside useEffect so it can be called from the toggle button
  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Get current date info for monthly calculations
        const now = new Date();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();

        // Create timestamps for current month start and end
        const currentMonthStart = new Date(currentYear, currentMonth, 1);
        const currentMonthEnd = new Date(currentYear, currentMonth + 1, 0, 23, 59, 59, 999);

        // Create timestamps for previous month
        const prevMonthStart = new Date(currentYear, currentMonth - 1, 1);
        const prevMonthEnd = new Date(currentYear, currentMonth, 0, 23, 59, 59, 999);

        // Convert to Firestore Timestamps
        const currentMonthStartTs = Timestamp.fromDate(currentMonthStart);
        const currentMonthEndTs = Timestamp.fromDate(currentMonthEnd);
        const prevMonthStartTs = Timestamp.fromDate(prevMonthStart);
        const prevMonthEndTs = Timestamp.fromDate(prevMonthEnd);

        // 1. Fetch Active Outlets - simply count all documents in the outlets collection
        let outletsSnapshot;
        let activeOutletsCount = 0;

        try {
          // Try with 'outlets' (lowercase o)
          const outletsQuery = query(collection(firestore, 'outlets'));
          outletsSnapshot = await getDocs(outletsQuery);
          activeOutletsCount = outletsSnapshot.size;
          console.log("Total outlets in 'outlets' collection:", activeOutletsCount);
        } catch (error) {
          console.log("Error fetching from 'outlets':", error);
          // Set default values if it fails
          outletsSnapshot = { forEach: () => {} }; // Empty forEach function
          activeOutletsCount = 0;
        }

        // For comparison, count outlets that existed before the end of last month
        let lastMonthActiveOutletsCount = 0;

        // Count outlets that were created before the end of last month
        outletsSnapshot.forEach(doc => {
          try {
            const outletData = doc.data();
            if (outletData.createdAt) {
              try {
                const creationDate = outletData.createdAt.toDate();
                if (creationDate <= prevMonthEnd) {
                  lastMonthActiveOutletsCount++;
                }
              } catch (error) {
                // If toDate() fails, assume it existed before last month
                console.log("Error converting createdAt to date for outlet:", error);
                lastMonthActiveOutletsCount++;
              }
            } else {
              // If no createdAt field, assume it existed before last month
              lastMonthActiveOutletsCount++;
            }
          } catch (error) {
            console.error("Error processing outlet document:", error);
          }
        });

        // Calculate percentage change
        const outletsChange = lastMonthActiveOutletsCount > 0
          ? (((activeOutletsCount - lastMonthActiveOutletsCount) / lastMonthActiveOutletsCount) * 100).toFixed(1)
          : 0;

        // 2. Fetch Active DS (from users collection with lowercase 'u') - count ALL users
        // Since there's no role field, we'll just count all documents in the users collection
        let activeDSCount = 0;
        let usersSnapshot;

        try {
          const usersQuery = query(collection(firestore, 'users'));
          usersSnapshot = await getDocs(usersQuery);
          activeDSCount = usersSnapshot.size;
          console.log("Total users in 'users' collection (Active DS):", activeDSCount);
        } catch (error) {
          console.error("Error fetching from 'users' collection:", error);
          usersSnapshot = { forEach: () => {} }; // Empty forEach function
        }

        // For comparison, count DS that existed before the end of last month
        let lastMonthActiveDSCount = 0;

        // Count users that were created before the end of last month
        usersSnapshot.forEach(doc => {
          try {
            const userData = doc.data();

            // Check if it was created before the end of last month
            if (userData.createdAt) {
              try {
                const creationDate = userData.createdAt.toDate();
                if (creationDate <= prevMonthEnd) {
                  lastMonthActiveDSCount++;
                }
              } catch (error) {
                // If toDate() fails, assume it existed before last month
                console.log("Error converting createdAt to date for user:", error);
                lastMonthActiveDSCount++;
              }
            } else {
              // If no createdAt field, assume it existed before last month
              lastMonthActiveDSCount++;
            }
          } catch (error) {
            console.error("Error processing user document:", error);
          }
        });

        // Calculate percentage change
        const dsChange = lastMonthActiveDSCount > 0
          ? (((activeDSCount - lastMonthActiveDSCount) / lastMonthActiveDSCount) * 100).toFixed(1)
          : 0;

        // 3. Fetch Active WD (from wdUsers collection) - count all WD users
        // Try both possible collection names: 'wdUsers' and 'wdusers'
        let wdSnapshot;
        let activeWDCount = 0;

        try {
          // First try with 'wdUsers' (capital U)
          const wdQuery = query(collection(firestore, 'wdUsers'));
          wdSnapshot = await getDocs(wdQuery);
          activeWDCount = wdSnapshot.size;
        } catch (error) {
          console.log("Error fetching from 'wdUsers', trying 'wdusers':", error);
          try {
            // If that fails, try with 'wdusers' (lowercase u)
            const wdQueryLowercase = query(collection(firestore, 'wdusers'));
            wdSnapshot = await getDocs(wdQueryLowercase);
            activeWDCount = wdSnapshot.size;
          } catch (error) {
            console.error("Error fetching from both 'wdUsers' and 'wdusers':", error);
            // Set default values if both fail
            wdSnapshot = { forEach: () => {} }; // Empty forEach function
            activeWDCount = 0;
          }
        }

        // For comparison, count WD that existed before the end of last month
        let lastMonthActiveWDCount = 0;

        // Count WD that were created before the end of last month
        wdSnapshot.forEach(doc => {
          try {
            const wdData = doc.data();
            if (wdData.createdAt) {
              try {
                const creationDate = wdData.createdAt.toDate();
                if (creationDate <= prevMonthEnd) {
                  lastMonthActiveWDCount++;
                }
              } catch (error) {
                // If toDate() fails, assume it existed before last month
                console.log("Error converting createdAt to date for WD:", error);
                lastMonthActiveWDCount++;
              }
            } else {
              // If no createdAt field, assume it existed before last month
              lastMonthActiveWDCount++;
            }
          } catch (error) {
            console.error("Error processing WD document:", error);
          }
        });

        // Calculate percentage change
        const wdChange = lastMonthActiveWDCount > 0
          ? (((activeWDCount - lastMonthActiveWDCount) / lastMonthActiveWDCount) * 100).toFixed(1)
          : 0;

        // 4. Fetch Bill Cut (count of orders)
        // Try both possible collection names: 'orders' and 'Orders'
        let ordersSnapshot;
        let billCutCount = 0;

        try {
          // First try with 'orders' (lowercase o)
          const ordersQuery = query(collection(firestore, 'orders'));
          ordersSnapshot = await getDocs(ordersQuery);
          billCutCount = ordersSnapshot.size;
          console.log("Successfully fetched from 'orders' collection:", billCutCount);
        } catch (error) {
          console.log("Error fetching from 'orders', trying 'Orders':", error);
          try {
            // If that fails, try with 'Orders' (capital O)
            const ordersQueryCapital = query(collection(firestore, 'Orders'));
            ordersSnapshot = await getDocs(ordersQueryCapital);
            billCutCount = ordersSnapshot.size;
            console.log("Successfully fetched from 'Orders' collection:", billCutCount);
          } catch (error) {
            console.error("Error fetching from both 'orders' and 'Orders':", error);
            // Set default values if both fail
            ordersSnapshot = { forEach: () => {} }; // Empty forEach function
            billCutCount = 0;
          }
        }

        // Count orders for current month and previous month
        let currentMonthOrdersCount = 0;
        let prevMonthOrdersCount = 0;

        // Process all orders and categorize by month
        ordersSnapshot.forEach(doc => {
          try {
            const orderData = doc.data();
            if (orderData.createdAt) {
              try {
                const orderDate = orderData.createdAt.toDate();

                // Check if order is from current month
                if (orderDate >= currentMonthStart && orderDate <= currentMonthEnd) {
                  currentMonthOrdersCount++;
                }

                // Check if order is from previous month
                if (orderDate >= prevMonthStart && orderDate <= prevMonthEnd) {
                  prevMonthOrdersCount++;
                }
              } catch (error) {
                console.log("Error converting createdAt to date for order:", error);
              }
            }
          } catch (error) {
            console.error("Error processing order document:", error);
          }
        });

        // Calculate percentage change
        const billCutChange = prevMonthOrdersCount > 0
          ? (((currentMonthOrdersCount - prevMonthOrdersCount) / prevMonthOrdersCount) * 100).toFixed(1)
          : 0;

        // 5. Fetch New Onboarding (outlets added this month)
        // We'll reuse the outlets snapshot we already have
        let newOutletsCount = 0;
        let prevMonthNewOutletsCount = 0;

        // Count new outlets for current month and previous month
        outletsSnapshot.forEach(doc => {
          try {
            const outletData = doc.data();
            if (outletData.createdAt) {
              try {
                const creationDate = outletData.createdAt.toDate();

                // Check if outlet was created in current month
                if (creationDate >= currentMonthStart && creationDate <= currentMonthEnd) {
                  newOutletsCount++;
                }

                // Check if outlet was created in previous month
                if (creationDate >= prevMonthStart && creationDate <= prevMonthEnd) {
                  prevMonthNewOutletsCount++;
                }
              } catch (error) {
                console.log("Error converting createdAt to date for new outlet:", error);
              }
            }
          } catch (error) {
            console.error("Error processing outlet document for new onboarding:", error);
          }
        });

        // Calculate percentage change
        const newOutletsChange = prevMonthNewOutletsCount > 0
          ? (((newOutletsCount - prevMonthNewOutletsCount) / prevMonthNewOutletsCount) * 100).toFixed(1)
          : 0;

        // 6. Calculate Sales (total amount from orders)
        // New calculation: Sum of totalQty from all orders multiplied by 105
        let totalSales = 0;
        let currentMonthSales = 0;
        let prevMonthSales = 0;

        // Calculate total sales and categorize by month
        ordersSnapshot.forEach(doc => {
          try {
            const orderData = doc.data();

            // Only process if totalQty exists and is a number
            if (orderData.totalQty && !isNaN(Number(orderData.totalQty))) {
              try {
                // Convert to number to ensure proper addition
                const qty = Number(orderData.totalQty);

                // Calculate amount (qty * 105)
                const amount = qty * 105;

                // Add to total sales
                totalSales += amount;

                // Check if order has a createdAt field
                if (orderData.createdAt) {
                  try {
                    const orderDate = orderData.createdAt.toDate();

                    // Add to current month sales if applicable
                    if (orderDate >= currentMonthStart && orderDate <= currentMonthEnd) {
                      currentMonthSales += amount;
                    }

                    // Add to previous month sales if applicable
                    if (orderDate >= prevMonthStart && orderDate <= prevMonthEnd) {
                      prevMonthSales += amount;
                    }
                  } catch (error) {
                    console.log("Error converting createdAt to date for sales calculation:", error);
                  }
                }
              } catch (error) {
                console.error("Error processing quantity for sales calculation:", error);
              }
            } else {
              console.log("Order missing totalQty or invalid value:", orderData.id || "unknown");
            }
          } catch (error) {
            console.error("Error processing order document for sales calculation:", error);
          }
        });

        console.log("Total sales calculation (sum of totalQty * 105):", totalSales);

        // Calculate percentage change
        const salesChange = prevMonthSales > 0
          ? (((currentMonthSales - prevMonthSales) / prevMonthSales) * 100).toFixed(1)
          : 0;

        // Format the sales value with Indian Rupee symbol
        const formattedSales = new Intl.NumberFormat('en-IN', {
          style: 'currency',
          currency: 'INR',
          maximumFractionDigits: 0
        }).format(totalSales);

        // Update dashboard data with real values
        setDashboardData({
          sales: {
            value: formattedSales,
            change: `${salesChange > 0 ? '+' : ''}${salesChange}%`,
            trend: salesChange >= 0 ? 'up' : 'down'
          },
          billCut: {
            value: billCutCount.toString(),
            change: `${billCutChange > 0 ? '+' : ''}${billCutChange}%`,
            trend: billCutChange >= 0 ? 'up' : 'down'
          },
          activeOutlets: {
            value: activeOutletsCount.toString(),
            change: `${outletsChange > 0 ? '+' : ''}${outletsChange}%`,
            trend: outletsChange >= 0 ? 'up' : 'down'
          },
          activeDS: {
            value: activeDSCount.toString(),
            change: `${dsChange > 0 ? '+' : ''}${dsChange}%`,
            trend: dsChange >= 0 ? 'up' : 'down'
          },
          activeWD: {
            value: activeWDCount.toString(),
            change: `${wdChange > 0 ? '+' : ''}${wdChange}%`,
            trend: wdChange >= 0 ? 'up' : 'down'
          },
          newOnboarding: {
            value: newOutletsCount.toString(),
            change: `${newOutletsChange > 0 ? '+' : ''}${newOutletsChange}%`,
            trend: newOutletsChange >= 0 ? 'up' : 'down'
          }
        });

      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

  useEffect(() => {
    // Call fetchDashboardData when component mounts
    fetchDashboardData();
  }, []); // Only fetch on component mount

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h1>Dashboard Overview</h1>
        <p className="dashboard-subtitle">Welcome to your admin dashboard. Here's a summary of your business metrics.</p>


      </div>

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading dashboard data...</p>
        </div>
      ) : (
        <>
          <div className="dashboard-stats-summary">
            <div className="stats-card">
              <div className="stats-icon sales">
                <MdAttachMoney />
              </div>
              <div className="stats-info">
                <h3>Total Sales</h3>
                <div className="stats-value">{dashboardData.sales.value}</div>
                <div className={`stats-change ${dashboardData.sales.trend}`}>
                  {dashboardData.sales.trend === 'up' ? <MdTrendingUp /> : <MdTrendingDown />}
                  {dashboardData.sales.change}
                </div>
              </div>
            </div>
          </div>

          <div className="dashboard-grid">
            <div className="dashboard-item">
              <div className="item-header">
                <div className="item-icon bill-cut">
                  <MdReceipt />
                </div>
                <h2>Bill Cut</h2>
              </div>
              <div className="item-value">{dashboardData.billCut.value}</div>
              <div className={`item-change ${dashboardData.billCut.trend}`}>
                {dashboardData.billCut.trend === 'up' ? <MdTrendingUp /> : <MdTrendingDown />}
                {dashboardData.billCut.change} from last month
              </div>
            </div>

            <div className="dashboard-item">
              <div className="item-header">
                <div className="item-icon outlets">
                  <MdStorefront />
                </div>
                <h2>Active Outlets</h2>
              </div>
              <div className="item-value">{dashboardData.activeOutlets.value}</div>
              <div className={`item-change ${dashboardData.activeOutlets.trend}`}>
                {dashboardData.activeOutlets.trend === 'up' ? <MdTrendingUp /> : <MdTrendingDown />}
                {dashboardData.activeOutlets.change} from last month
              </div>
            </div>

            <div className="dashboard-item">
              <div className="item-header">
                <div className="item-icon ds">
                  <MdPeople />
                </div>
                <h2>Active DS</h2>
              </div>
              <div className="item-value">{dashboardData.activeDS.value}</div>
              <div className={`item-change ${dashboardData.activeDS.trend}`}>
                {dashboardData.activeDS.trend === 'up' ? <MdTrendingUp /> : <MdTrendingDown />}
                {dashboardData.activeDS.change} from last month
              </div>
            </div>

            <div className="dashboard-item">
              <div className="item-header">
                <div className="item-icon wd">
                  <MdLocalShipping />
                </div>
                <h2>Active WD</h2>
              </div>
              <div className="item-value">{dashboardData.activeWD.value}</div>
              <div className={`item-change ${dashboardData.activeWD.trend}`}>
                {dashboardData.activeWD.trend === 'up' ? <MdTrendingUp /> : <MdTrendingDown />}
                {dashboardData.activeWD.change} from last month
              </div>
            </div>

            <div className="dashboard-item">
              <div className="item-header">
                <div className="item-icon onboarding">
                  <MdPersonAdd />
                </div>
                <h2>New Onboarding</h2>
              </div>
              <div className="item-value">{dashboardData.newOnboarding.value}</div>
              <div className={`item-change ${dashboardData.newOnboarding.trend}`}>
                {dashboardData.newOnboarding.trend === 'up' ? <MdTrendingUp /> : <MdTrendingDown />}
                {dashboardData.newOnboarding.change} from last month
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Dashboard;
