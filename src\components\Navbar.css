.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: var(--navbar-height);
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 0 1.5rem;
  position: sticky;
  top: 0;
  z-index: 50;
}

.navbar-left, .navbar-right {
  display: flex;
  align-items: center;
}

.navbar-center h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e40af;
}

/* Menu <PERSON> */
.menu-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #64748b;
  cursor: pointer;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.menu-button:hover {
  background-color: #f1f5f9;
  color: #334155;
}

/* Search Container */
.search-container {
  display: flex;
  align-items: center;
  background-color: #f1f5f9;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  width: 300px;
}

.search-icon {
  color: #64748b;
  margin-right: 0.5rem;
}

.search-input {
  background: none;
  border: none;
  outline: none;
  color: #334155;
  width: 100%;
  font-size: 0.875rem;
}

.search-input::placeholder {
  color: #94a3b8;
}

/* Date and Time */
.date-time {
  margin-right: 1.5rem;
  text-align: right;
}

.time {
  font-size: 1.125rem;
  font-weight: 600;
  color: #334155;
}

.date {
  font-size: 0.75rem;
  color: #64748b;
}

/* Notification Icon */
.notification-icon {
  position: relative;
  font-size: 1.5rem;
  color: #64748b;
  margin-right: 1.5rem;
  cursor: pointer;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ef4444;
  color: white;
  font-size: 0.625rem;
  font-weight: 600;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* User Profile */
.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.avatar {
  width: 40px;
  height: 40px;
  background-color: #1e40af;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 0.75rem;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #334155;
  font-size: 0.875rem;
}

.user-role {
  color: #64748b;
  font-size: 0.75rem;
}